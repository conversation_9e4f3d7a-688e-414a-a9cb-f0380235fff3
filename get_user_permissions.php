<?php
require_once 'permission_check.php';

// التحقق من صلاحية إدارة الصلاحيات
checkPermission('manage_account_permissions');

$key = getenv('ENCRYPTION_KEY');
$account_id = decrypt($_GET['account_id'], $key);

if (!$account_id) {
    echo '<p>خطأ: معرف المستخدم غير صحيح</p>';
    exit();
}

// جلب معلومات المستخدم
$user_query = "
    SELECT a.username, a.name, r.role_name, r.role_description
    FROM accounts a
    LEFT JOIN roles r ON a.role_id = r.role_id
    WHERE a.account_id = ?
";
$stmt = $conn->prepare($user_query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();
$stmt->close();

// جلب صلاحيات الدور
$role_permissions_query = "
    SELECT p.permission_id, p.permission_name, p.permission_description, p.module_name
    FROM accounts a
    JOIN roles r ON a.role_id = r.role_id
    JOIN role_permissions rp ON r.role_id = rp.role_id
    JOIN permissions p ON rp.permission_id = p.permission_id
    WHERE a.account_id = ?
    ORDER BY p.module_name, p.permission_description
";
$stmt = $conn->prepare($role_permissions_query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$role_permissions = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// جلب الصلاحيات الإضافية للمستخدم
$user_permissions_query = "
    SELECT p.permission_id, p.permission_name, p.permission_description, p.module_name, 
           up.permission_type, up.expires_at
    FROM user_permissions up
    JOIN permissions p ON up.permission_id = p.permission_id
    WHERE up.account_id = ? AND (up.expires_at IS NULL OR up.expires_at > NOW())
    ORDER BY p.module_name, p.permission_description
";
$stmt = $conn->prepare($user_permissions_query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$user_permissions = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// جلب جميع الصلاحيات المتاحة
$all_permissions_query = "
    SELECT permission_id, permission_name, permission_description, module_name
    FROM permissions
    ORDER BY module_name, permission_description
";
$all_permissions = $conn->query($all_permissions_query)->fetch_all(MYSQLI_ASSOC);

// تجميع الصلاحيات حسب الوحدة
$permissions_by_module = [];
foreach ($all_permissions as $permission) {
    $permissions_by_module[$permission['module_name']][] = $permission;
}

// إنشاء مصفوفة للصلاحيات الحالية
$current_role_permissions = array_column($role_permissions, 'permission_id');
$current_user_permissions = [];
foreach ($user_permissions as $perm) {
    $current_user_permissions[$perm['permission_id']] = $perm['permission_type'];
}

function getModuleDisplayName($module) {
    $names = [
        'accounts' => 'إدارة الحسابات',
        'stores' => 'إدارة الفروع',
        'categories' => 'إدارة التصنيفات',
        'items' => 'إدارة الأصناف',
        'invoices' => 'فواتير البيع',
        'purchase_invoices' => 'فواتير الشراء',
        'wholesale_invoices' => 'فواتير الجملة',
        'expenses' => 'المصروفات',
        'balance_transfers' => 'التحويلات',
        'inventory' => 'الجرد',
        'shift_closures' => 'إقفال الوردية',
        'reports' => 'التقارير',
        'system' => 'إعدادات النظام'
    ];
    
    return $names[$module] ?? $module;
}
?>

<style>
    .user-info {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .permission-module {
        margin-bottom: 20px;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
    }
    
    .module-header {
        background: var(--color-primary);
        color: white;
        padding: 10px 15px;
        font-weight: bold;
    }
    
    .permission-list {
        padding: 15px;
    }
    
    .permission-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }
    
    .permission-row:last-child {
        border-bottom: none;
    }
    
    .permission-info {
        flex: 1;
    }
    
    .permission-name {
        font-weight: bold;
        color: #333;
    }
    
    .permission-desc {
        font-size: 0.9em;
        color: #666;
    }
    
    .permission-status {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        font-weight: bold;
    }
    
    .status-role {
        background: #28a745;
        color: white;
    }
    
    .status-granted {
        background: #17a2b8;
        color: white;
    }
    
    .status-denied {
        background: #dc3545;
        color: white;
    }
    
    .status-none {
        background: #6c757d;
        color: white;
    }
    
    .permission-actions {
        display: flex;
        gap: 5px;
    }
    
    .btn-mini {
        padding: 2px 6px;
        font-size: 0.7em;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    }
    
    .btn-grant {
        background: #28a745;
        color: white;
    }
    
    .btn-deny {
        background: #dc3545;
        color: white;
    }
    
    .btn-remove {
        background: #6c757d;
        color: white;
    }
</style>

<div class="user-info">
    <h3><?= htmlspecialchars($user['name'] ?: $user['username']) ?></h3>
    <p><strong>اسم المستخدم:</strong> <?= htmlspecialchars($user['username']) ?></p>
    <p><strong>الدور الحالي:</strong> <?= htmlspecialchars($user['role_description'] ?: 'بدون دور') ?></p>
</div>

<?php foreach ($permissions_by_module as $module => $module_permissions): ?>
    <div class="permission-module">
        <div class="module-header">
            <?= getModuleDisplayName($module) ?>
        </div>
        <div class="permission-list">
            <?php foreach ($module_permissions as $permission): ?>
                <?php
                $has_role_permission = in_array($permission['permission_id'], $current_role_permissions);
                $user_permission_type = $current_user_permissions[$permission['permission_id']] ?? null;
                ?>
                <div class="permission-row">
                    <div class="permission-info">
                        <div class="permission-name"><?= htmlspecialchars($permission['permission_description']) ?></div>
                        <div class="permission-desc"><?= htmlspecialchars($permission['permission_name']) ?></div>
                    </div>
                    
                    <div class="permission-status">
                        <?php if ($user_permission_type === 'grant'): ?>
                            <span class="status-badge status-granted">ممنوحة إضافياً</span>
                        <?php elseif ($user_permission_type === 'deny'): ?>
                            <span class="status-badge status-denied">مرفوضة</span>
                        <?php elseif ($has_role_permission): ?>
                            <span class="status-badge status-role">من الدور</span>
                        <?php else: ?>
                            <span class="status-badge status-none">غير متاحة</span>
                        <?php endif; ?>
                        
                        <div class="permission-actions">
                            <?php if ($user_permission_type !== 'grant'): ?>
                                <button class="btn-mini btn-grant" 
                                        onclick="grantPermission('<?= encrypt($account_id, $key) ?>', <?= $permission['permission_id'] ?>)">
                                    منح
                                </button>
                            <?php endif; ?>
                            
                            <?php if ($user_permission_type !== 'deny' && $has_role_permission): ?>
                                <button class="btn-mini btn-deny" 
                                        onclick="denyPermission('<?= encrypt($account_id, $key) ?>', <?= $permission['permission_id'] ?>)">
                                    رفض
                                </button>
                            <?php endif; ?>
                            
                            <?php if ($user_permission_type): ?>
                                <button class="btn-mini btn-remove" 
                                        onclick="removePermission('<?= encrypt($account_id, $key) ?>', <?= $permission['permission_id'] ?>)">
                                    إزالة
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
<?php endforeach; ?>

<script>
    function grantPermission(accountId, permissionId) {
        fetch('permissions_management.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `action=grant_permission&account_id=${accountId}&permission_id=${permissionId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire('نجح!', data.message, 'success').then(() => {
                    manageUserPermissions(accountId, document.getElementById('userPermissionsTitle').textContent.split(': ')[1]);
                });
            } else {
                Swal.fire('خطأ!', data.message, 'error');
            }
        });
    }
    
    function denyPermission(accountId, permissionId) {
        fetch('permissions_management.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `action=grant_permission&account_id=${accountId}&permission_id=${permissionId}&permission_type=deny`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire('نجح!', 'تم رفض الصلاحية بنجاح', 'success').then(() => {
                    manageUserPermissions(accountId, document.getElementById('userPermissionsTitle').textContent.split(': ')[1]);
                });
            } else {
                Swal.fire('خطأ!', data.message, 'error');
            }
        });
    }
    
    function removePermission(accountId, permissionId) {
        fetch('permissions_management.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `action=revoke_permission&account_id=${accountId}&permission_id=${permissionId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire('نجح!', data.message, 'success').then(() => {
                    manageUserPermissions(accountId, document.getElementById('userPermissionsTitle').textContent.split(': ')[1]);
                });
            } else {
                Swal.fire('خطأ!', data.message, 'error');
            }
        });
    }
</script>
